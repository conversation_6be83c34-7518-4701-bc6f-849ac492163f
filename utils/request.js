// API配置
const config = {
	baseURL: "https://iot.api.com/JCloud",
	appKey: "acacc54ca8196d370e7defd57528f934", // 水滴
	timeout: 10000, // 请求超时时间
	retryCount: 3, // 重试次数
	showLoading: true, // 是否显示loading
	loadingText: '加载中...'
}

// 请求拦截器
const requestInterceptors = []
// 响应拦截器
const responseInterceptors = []

function request(options) {
	console.log('API信息', options);

	// 合并默认配置
	const finalOptions = Object.assign({
		method: 'GET',
		showLoading: config.showLoading,
		loadingText: config.loadingText,
		timeout: config.timeout
	}, options);

	const userInfo = wx.getStorageSync('userInfo');
	const header = {
		"content-type": 'application/json',
		"X-App-Key": config.appKey
	}

	// 登录后的token
	if (userInfo && userInfo.token) {
		header['X-User-Token'] = userInfo.token;
	}

	// form-data数据
	if (finalOptions.boundary) {
		console.log('boundary');
		header['content-type'] = `multipart/form-data; boundary=${finalOptions.boundary}`
	}

	// 获取验证码特殊处理
	if (finalOptions.url == '/users/captcha') {
		console.log('X-App-Language: en');
		header['X-App-Language'] = 'en'
	}

	// 平台类型
	if (finalOptions.Platform) {
		header['X-App-Platform'] = finalOptions.Platform
	}

	// 执行请求拦截器
	requestInterceptors.forEach(interceptor => {
		if (typeof interceptor === 'function') {
			interceptor(finalOptions, header);
		}
	});
	// 显示loading
	if (finalOptions.showLoading) {
		wx.showLoading({
			title: finalOptions.loadingText,
			mask: true
		});
	}

	// 文件上传处理
	if (finalOptions.type && finalOptions.type === 'uploadFile') {
		console.log('图片上传');
		return new Promise((resolve, reject) => {
			header['content-type'] = 'multipart/form-data'
			wx.uploadFile({
				header: header,
				url: config.baseURL + finalOptions.url,
				method: finalOptions.method,
				name: 'file',
				filePath: finalOptions.data.file.url,
				formData: {
					path: finalOptions.data.path,
					file: finalOptions.file
				},
				success(res) {
					console.log('upload res', res);
					if (finalOptions.showLoading) {
						wx.hideLoading();
					}
					if (res.statusCode === 200) {
						const result = JSON.parse(res.data);
						// 执行响应拦截器
						responseInterceptors.forEach(interceptor => {
							if (typeof interceptor === 'function') {
								interceptor(result, finalOptions);
							}
						});
						resolve(result);
					} else {
						reject(res);
					}
				},
				fail(err) {
					if (finalOptions.showLoading) {
						wx.hideLoading();
					}
					reject(err);
				}
			});
		})
	} else {
		return new Promise((resolve, reject) => {
			// 发起请求
			const requestTask = wx.request({
				header: header,
				url: config.baseURL + finalOptions.url,
				method: finalOptions.method,
				data: finalOptions.data,
				timeout: finalOptions.timeout,
				success: res => {
					if (finalOptions.showLoading) {
						wx.hideLoading();
					}

					if (res.data) {
						// token失效处理
						if (res.data.result_code === 'sis.api.InvalidToken' || res.data.result_code == 'sis.api.ExpiredToken') {
							console.log('token失效', res.data);
							wx.showToast({
								title: '超时，请重新登录',
								icon: 'error'
							})
							setTimeout(() => {
								wx.reLaunch({
									url: '/pages/login/login',
								})
								wx.clearStorage()
							}, 500)
							reject(res.data);
						} else {
							// 执行响应拦截器
							responseInterceptors.forEach(interceptor => {
								if (typeof interceptor === 'function') {
									interceptor(res.data, finalOptions);
								}
							});
							resolve(res.data);
						}
					} else {
						reject(res.data);
					}
				},
				fail: err => {
					if (finalOptions.showLoading) {
						wx.hideLoading();
					}
					reject(err);
				},
				complete: () => {
					if (finalOptions.showLoading) {
						wx.hideLoading();
					}
				}
			});

			// 添加请求中断方法
			if (finalOptions.abortRequest) {
				finalOptions.abortRequest(requestTask);
			}
		});
	}
}

// 添加请求拦截器
request.addRequestInterceptor = function(interceptor) {
	if (typeof interceptor === 'function') {
		requestInterceptors.push(interceptor);
	}
}

// 添加响应拦截器
request.addResponseInterceptor = function(interceptor) {
	if (typeof interceptor === 'function') {
		responseInterceptors.push(interceptor);
	}
}

// 设置全局配置
request.setConfig = function(newConfig) {
	Object.assign(config, newConfig);
}

// 获取当前配置
request.getConfig = function() {
	return {...config};
}

module.exports = request
